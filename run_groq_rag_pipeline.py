"""
Groq RAG pipeline runner for Jhajjar Power Plant extraction.
Uses the simplified pipeline with Groq and RAG-based extraction for missing fields.
"""
import asyncio
import logging
import json
import sys
import time
from datetime import datetime
from src.simple_pipeline import SimplePowerPlantPipeline
from src.enhanced_extractor import AdaptiveExtractor
from src.plant_details_extractor import PlantDetailsExtractor
from src.config import config


class GroqRAGPipeline(SimplePowerPlantPipeline):
    """Simplified pipeline using Groq with RAG for missing fields."""

    def __init__(self):
        """Initialize the Groq RAG pipeline."""
        self.serp_api_key = config.pipeline.serp_api_key
        from src.scraper_client import ScraperAPIClient
        self.scraper_client = ScraperAPIClient(config.pipeline.scraper_api_key)

        # Use Groq for both extractors
        self.enhanced_extractor = AdaptiveExtractor(config.pipeline.groq_api_key)
        from src.groq_client import GroqExtractionClient
        self.plant_extractor = PlantDetailsExtractor(
            groq_client=GroqExtractionClient(config.pipeline.groq_api_key)
        )


def setup_logging():
    """Configure logging for the pipeline."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('pipeline_groq_rag.log'),
            logging.StreamHandler()
        ]
    )


async def extract_jhajjar_groq_rag():
    """
    Main extraction function for Jhajjar Power Plant using Groq with RAG.
    """
    plant_name = "Jhajjar Power Plant"
    start_time = time.time()

    try:
        print("🚀 GROQ RAG PIPELINE - JHAJJAR POWER PLANT EXTRACTION")
        print("=" * 60)
        print(f"🔍 Target Plant: {plant_name}")
        print(f"🧠 Method: Simplified Pipeline with Groq + RAG")
        print(f"📊 Strategy: Clean prompts → Top 5 links → Cache + RAG for missing fields")
        print("=" * 60)

        # Initialize the Groq RAG pipeline
        print("\n⚙️  Initializing Groq RAG pipeline...")
        pipeline = GroqRAGPipeline()
        print("✅ Groq RAG pipeline initialized successfully")

        # Extract plant data
        print(f"\n🔍 Starting main extraction for: {plant_name}")
        org_details, plant_details, extraction_info = await pipeline.extract_plant_data(plant_name)

        total_duration = time.time() - start_time

        # Save results with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        org_file = f"jhajjar_org_groq_rag_{timestamp}.json"
        plant_file = f"jhajjar_plant_groq_rag_{timestamp}.json"
        info_file = f"jhajjar_extraction_info_groq_rag_{timestamp}.json"

        await pipeline.save_results(
            org_details, plant_details, extraction_info,
            org_file, plant_file, info_file
        )

        print(f"\n⏱️  Total extraction time: {total_duration:.1f} seconds")

        # Display extraction metrics
        print(f"\n📊 EXTRACTION METRICS")
        print("-" * 40)
        print(f"🔍 Initial search time: {extraction_info.get('search_time', 0):.1f}s")
        print(f"📄 Total pages scraped: {extraction_info.get('pages_scraped', 0)}")
        print(f"💾 Cache hit fields: {len(extraction_info.get('cache_hit_fields', []))}")
        print(f"🎯 Missing field searches: {extraction_info.get('missing_field_searches', 0)}")

        cache_fields = extraction_info.get('cache_hit_fields', [])
        if cache_fields:
            print(f"✅ Fields from cache: {', '.join(cache_fields)}")

        targeted_searches = extraction_info.get('missing_field_searches_list', [])
        if targeted_searches:
            print(f"🔍 RAG extractions: {', '.join(targeted_searches)}")

        # Display results summary
        print(f"\n📋 EXTRACTION RESULTS SUMMARY")
        print("=" * 50)

        if org_details:
            # Handle both dictionary and model object
            if hasattr(org_details, 'model_dump'):
                org_data = org_details.model_dump()
            else:
                org_data = org_details

            filled_org_fields = sum(1 for v in org_data.values() if v not in [None, "", []])
            print(f"📊 Organizational Details: {filled_org_fields}/{len(org_data)} fields extracted")

            # Show key organizational info
            key_org_fields = {
                'organization_name': '🏢 Organization',
                'country_name': '🌍 Country',
                'province': '📍 Province',
                'plant_types': '⚡ Plant Types',
                'cfpp_type': '🏛️ Type',
                'plants_count': '🏭 Plants Count',
                'financial_year': '📅 Financial Year'
            }

            for field, label in key_org_fields.items():
                value = org_data.get(field)
                if value and value not in [None, "", []]:
                    if isinstance(value, list):
                        print(f"   {label}: {', '.join(map(str, value))}")
                    else:
                        print(f"   {label}: {value}")

        if plant_details:
            # Handle both dictionary and model object
            if hasattr(plant_details, 'model_dump'):
                plant_data = plant_details.model_dump()
            else:
                plant_data = plant_details

            filled_plant_fields = sum(1 for v in plant_data.values() if v not in [None, "", []])
            print(f"\n🔧 Plant Technical Details: {filled_plant_fields}/{len(plant_data)} fields extracted")

            # Show key technical info
            key_plant_fields = {
                'name': '📛 Plant Name',
                'plant_type': '⚙️ Plant Type',
                'plant_address': '📍 Address',
                'lat': '🌐 Latitude',
                'long': '🌐 Longitude',
                'units_id': '🔢 Units'
            }

            for field, label in key_plant_fields.items():
                value = plant_data.get(field)
                if value and value not in [None, "", []]:
                    if isinstance(value, list):
                        print(f"   {label}: {', '.join(map(str, value))}")
                    else:
                        print(f"   {label}: {value}")

            # Show complex fields summary
            if plant_data.get('grid_connectivity_maps'):
                print(f"   🔌 Grid Connectivity: {len(plant_data['grid_connectivity_maps'])} connections")

            if plant_data.get('ppa_details'):
                print(f"   📄 PPA Details: {len(plant_data['ppa_details'])} agreements")

        print(f"\n💾 Results saved to:")
        print(f"   📊 Organizational: {org_file}")
        print(f"   🔧 Plant Technical: {plant_file}")
        print(f"   📈 Extraction Info: {info_file}")

        # Display clean JSON results
        print(f"\n📄 CLEAN JSON RESULTS")
        print("=" * 50)

        if org_details:
            print(f"\n📊 ORGANIZATIONAL DETAILS JSON:")
            print("-" * 40)
            if hasattr(org_details, 'model_dump'):
                print(json.dumps(org_details.model_dump(), indent=2, ensure_ascii=False))
            else:
                print(json.dumps(org_details, indent=2, ensure_ascii=False))

        if plant_details:
            print(f"\n🔧 PLANT TECHNICAL DETAILS JSON:")
            print("-" * 40)
            if hasattr(plant_details, 'model_dump'):
                print(json.dumps(plant_details.model_dump(), indent=2, ensure_ascii=False))
            else:
                print(json.dumps(plant_details, indent=2, ensure_ascii=False))

        print(f"\n🎉 Groq RAG Jhajjar Power Plant extraction completed successfully!")
        print(f"📊 RAG extraction helped avoid API rate limits for missing fields!")
        print(f"⏱️  Total time: {total_duration:.1f} seconds")

        return org_details, plant_details, extraction_info

    except Exception as e:
        print(f"\n❌ Extraction failed for {plant_name}: {e}")
        logging.error(f"Extraction failed for {plant_name}: {e}", exc_info=True)
        raise


async def main():
    """Main execution function."""
    setup_logging()

    print("🚀 GROQ RAG PIPELINE - JHAJJAR POWER PLANT EXTRACTION")
    print("Using simplified pipeline with Groq and RAG-based extraction for missing fields")
    print("No API rate limits for targeted field extraction!")
    print()

    try:
        # Run the extraction
        org_details, plant_details, extraction_info = await extract_jhajjar_groq_rag()

        print(f"\n✅ GROQ RAG PIPELINE COMPLETED SUCCESSFULLY!")
        print("Check the generated JSON files for complete results.")

    except Exception as e:
        print(f"\n❌ GROQ RAG PIPELINE FAILED: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
