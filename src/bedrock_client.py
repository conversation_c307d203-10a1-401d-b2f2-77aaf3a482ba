"""
AWS Bedrock client for LLM extraction using Llama3.
"""
import asyncio
import logging
import json
import boto3
import os
from typing import Optional, Dict, Any, List
from langchain_aws import BedrockLLM

logger = logging.getLogger(__name__)


class BedrockExtractionClient:
    """AWS Bedrock client for power plant data extraction."""

    def __init__(self, aws_access_key_id: str, aws_secret_access_key: str, region_name: str = "us-east-1"):
        """
        Initialize Bedrock client.

        Args:
            aws_access_key_id: AWS access key ID
            aws_secret_access_key: AWS secret access key
            region_name: AWS region (default: us-east-1 for Llama3)
        """
        self.aws_access_key_id = aws_access_key_id
        self.aws_secret_access_key = aws_secret_access_key
        self.region_name = region_name

        # Set environment variables
        os.environ["AWS_ACCESS_KEY_ID"] = aws_access_key_id
        os.environ["AWS_SECRET_ACCESS_KEY"] = aws_secret_access_key

        # Initialize Bedrock client
        self.bedrock_client = boto3.client(
            service_name="bedrock-runtime",
            region_name=region_name
        )

        # Initialize Llama3 model
        self.llm = BedrockLLM(
            client=self.bedrock_client,
            model_id="meta.llama3-8b-instruct-v1:0",
            model_kwargs={
                "temperature": 0.3,  # Lower temperature for more consistent extraction
                "max_gen_len": 1024  # Increased for complex JSON responses
            }
        )

        logger.info(f"Initialized Bedrock client with Llama3 in region {region_name}")

    async def extract_field(self, field_name: str = None, content: str = None, plant_name: str = None, organization_name: str = None) -> Optional[str]:
        """
        Extract a field using Bedrock Llama3 - compatible with Groq interface.

        Args:
            field_name: Name of the field to extract (for compatibility)
            content: Content to extract from (for compatibility)
            plant_name: Plant name (for compatibility)
            organization_name: Organization name (for compatibility)

        Returns:
            Extracted field value or None
        """
        # If called with old interface (just prompt), handle it
        if isinstance(field_name, str) and content is None and plant_name is None:
            prompt = field_name  # field_name is actually the prompt
            return await self._extract_with_prompt(prompt)

        # If called with Groq-compatible interface, build the prompt and return ExtractionResult
        if field_name and content:
            from src.config import config
            from src.models import ExtractionResult

            # Try both organizational and plant details prompts
            prompts = config.extraction_prompts
            plant_prompts = config.plant_details_extraction_prompts

            # Check which prompt set has the field
            if field_name in prompts:
                prompt_template = prompts[field_name]
            elif field_name in plant_prompts:
                prompt_template = plant_prompts[field_name]
            else:
                logger.warning(f"No prompt found for field: {field_name}")
                return ExtractionResult(
                    field_name=field_name,
                    extracted_value=None,
                    confidence_score=0.0,
                    source_url="error",
                    extraction_method="bedrock_llm",
                    source_type="error"
                )

            # Format the prompt
            try:
                if organization_name:
                    prompt = prompt_template.format(
                        plant_name=plant_name or "",
                        content=content[:8000],  # Limit content length
                        organization_name=organization_name
                    )
                else:
                    prompt = prompt_template.format(
                        plant_name=plant_name or "",
                        content=content[:8000]
                    )

                # Extract using Bedrock
                raw_response = await self._extract_with_prompt(prompt)

                if raw_response:
                    # Process the response (simplified for now)
                    extracted_value, confidence = self._process_response(field_name, raw_response)

                    return ExtractionResult(
                        field_name=field_name,
                        extracted_value=extracted_value,
                        confidence_score=confidence,
                        source_url="aggregated",
                        extraction_method="bedrock_llm",
                        source_type="combined"
                    )
                else:
                    return ExtractionResult(
                        field_name=field_name,
                        extracted_value=None,
                        confidence_score=0.0,
                        source_url="error",
                        extraction_method="bedrock_llm",
                        source_type="error"
                    )

            except Exception as e:
                logger.error(f"Bedrock extraction failed for field {field_name}: {e}")
                return ExtractionResult(
                    field_name=field_name,
                    extracted_value=None,
                    confidence_score=0.0,
                    source_url="error",
                    extraction_method="bedrock_llm",
                    source_type="error"
                )

        return None

    async def _extract_with_prompt(self, prompt: str) -> Optional[str]:
        """
        Internal method to extract using a formatted prompt.

        Args:
            prompt: Extraction prompt

        Returns:
            Extracted field value or None
        """
        try:
            logger.debug(f"Bedrock extraction prompt length: {len(prompt)}")

            # Run in thread pool since langchain is synchronous
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(None, self._sync_extract, prompt)

            if response:
                logger.debug(f"Bedrock extraction response length: {len(response)}")
                return response.strip()
            else:
                logger.warning("Empty response from Bedrock")
                return None

        except Exception as e:
            logger.error(f"Bedrock extraction failed: {e}")
            return None

    def _sync_extract(self, prompt: str) -> Optional[str]:
        """Synchronous extraction method for thread pool execution."""
        try:
            # Format prompt for Llama3
            formatted_prompt = f"""<|begin_of_text|><|start_header_id|>user<|end_header_id|>

{prompt}

Please provide a clear, concise answer based on the content provided.<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""

            response = self.llm.invoke(formatted_prompt)
            return response

        except Exception as e:
            logger.error(f"Sync Bedrock extraction failed: {e}")
            return None

    async def extract_multiple_fields(self, prompts: Dict[str, str]) -> Dict[str, Optional[str]]:
        """
        Extract multiple fields concurrently.

        Args:
            prompts: Dictionary of field_name -> prompt

        Returns:
            Dictionary of field_name -> extracted_value
        """
        tasks = []
        field_names = []

        for field_name, prompt in prompts.items():
            task = self.extract_field(prompt)
            tasks.append(task)
            field_names.append(field_name)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        extracted_fields = {}
        for field_name, result in zip(field_names, results):
            if isinstance(result, Exception):
                logger.error(f"Error extracting {field_name}: {result}")
                extracted_fields[field_name] = None
            else:
                extracted_fields[field_name] = result

        return extracted_fields

    async def extract_json_field(self, prompt: str, expected_structure: str = "object") -> Optional[Any]:
        """
        Extract a field that should return JSON.

        Args:
            prompt: Extraction prompt
            expected_structure: "object", "array", or "any"

        Returns:
            Parsed JSON object or None
        """
        try:
            raw_response = await self.extract_field(prompt)

            if not raw_response:
                return None

            # Try to find and parse JSON in the response
            json_str = self._extract_json_from_response(raw_response)

            if json_str:
                try:
                    parsed_json = json.loads(json_str)

                    # Validate structure if specified
                    if expected_structure == "object" and not isinstance(parsed_json, dict):
                        logger.warning(f"Expected object but got {type(parsed_json)}")
                        return None
                    elif expected_structure == "array" and not isinstance(parsed_json, list):
                        logger.warning(f"Expected array but got {type(parsed_json)}")
                        return None

                    return parsed_json

                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse JSON: {e}")
                    return None
            else:
                logger.warning("No JSON found in response")
                return None

        except Exception as e:
            logger.error(f"JSON extraction failed: {e}")
            return None

    def _extract_json_from_response(self, response: str) -> Optional[str]:
        """Extract JSON string from LLM response."""
        try:
            # Look for JSON patterns
            import re

            # Find JSON objects
            obj_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
            obj_matches = re.findall(obj_pattern, response, re.DOTALL)

            # Find JSON arrays
            arr_pattern = r'\[[^\[\]]*(?:\[[^\[\]]*\][^\[\]]*)*\]'
            arr_matches = re.findall(arr_pattern, response, re.DOTALL)

            # Try object matches first
            for match in obj_matches:
                try:
                    json.loads(match)
                    return match
                except:
                    continue

            # Try array matches
            for match in arr_matches:
                try:
                    json.loads(match)
                    return match
                except:
                    continue

            # If no valid JSON found, try to extract from code blocks
            code_pattern = r'```(?:json)?\s*(\{.*?\}|\[.*?\])\s*```'
            code_matches = re.findall(code_pattern, response, re.DOTALL)

            for match in code_matches:
                try:
                    json.loads(match)
                    return match
                except:
                    continue

            return None

        except Exception as e:
            logger.error(f"Error extracting JSON from response: {e}")
            return None

    def _process_response(self, field_name: str, raw_response: str) -> tuple:
        """Process and validate LLM response - simplified version."""
        response_lower = raw_response.lower().strip()

        # Handle "unknown" responses
        if any(term in response_lower for term in ["unknown", "unclear", "not found", "not mentioned"]):
            return None, 0.1

        # Basic field-specific processing (simplified)
        if field_name == "cfpp_type":
            # Check for ownership types
            if any(term in response_lower for term in ["private", "privately owned"]):
                return "private", 0.8
            elif any(term in response_lower for term in ["public", "government", "state"]):
                return "public", 0.8
            elif any(term in response_lower for term in ["joint venture", "partnership"]):
                return "joint_venture", 0.8
            else:
                return raw_response.strip(), 0.6

        elif field_name == "plants_count":
            # Extract number
            import re
            numbers = re.findall(r'\d+', raw_response)
            if numbers:
                try:
                    count = int(numbers[0])
                    if 1 <= count <= 1000:
                        return count, 0.8
                except ValueError:
                    pass
            return None, 0.1

        elif field_name == "plant_types":
            # Split by common delimiters
            import re
            types_raw = re.split(r'[,;|\n]', response_lower)
            valid_types = []
            known_types = ["coal", "gas", "nuclear", "solar", "wind", "hydro", "biomass", "geothermal", "oil"]

            for type_raw in types_raw:
                type_clean = type_raw.strip()
                if type_clean in known_types:
                    valid_types.append(type_clean)

            if valid_types:
                return valid_types, 0.8
            return [], 0.1

        elif field_name == "currency_in":
            # Extract 3-letter currency codes
            import re
            currency_match = re.search(r'\b[A-Z]{3}\b', raw_response.upper())
            if currency_match:
                currency = currency_match.group()
                common_currencies = ["USD", "EUR", "GBP", "JPY", "CNY", "INR", "CAD", "AUD", "BRL", "MXN"]
                if currency in common_currencies:
                    return currency, 0.9
                else:
                    return currency, 0.6
            return None, 0.1

        elif field_name == "financial_year":
            # Extract MM-MM format from response
            import re
            mm_mm_pattern = r'\b((0[1-9]|1[0-2])-(0[1-9]|1[0-2]))\b'
            mm_mm_match = re.search(mm_mm_pattern, raw_response)
            if mm_mm_match:
                return mm_mm_match.group(1), 0.9

            # Check for common patterns
            response_clean = raw_response.lower()
            if any(term in response_clean for term in ['april to march', '04-03']):
                return '04-03', 0.8
            elif any(term in response_clean for term in ['january to december', '01-12']):
                return '01-12', 0.8
            elif any(term in response_clean for term in ['july to june', '07-06']):
                return '07-06', 0.8

            return None, 0.1

        elif field_name in ["grid_connectivity_maps", "ppa_details"]:
            # Handle JSON fields with Pydantic parser
            return self._parse_nested_json_with_pydantic(field_name, raw_response)

        elif field_name == "organization_name":
            # Clean organization name
            import re
            # Remove common prefixes and clean up
            cleaned = raw_response.strip()

            # Remove verbose prefixes
            prefixes_to_remove = [
                r'^.*?official company name.*?is:?\s*',
                r'^.*?company.*?that owns.*?is:?\s*',
                r'^.*?organization.*?is:?\s*',
                r'^.*?name.*?is:?\s*',
                r'^.*?is:?\s*',
                r'^the\s+',
                r'^company:?\s*',
                r'^organization:?\s*'
            ]

            for prefix in prefixes_to_remove:
                cleaned = re.sub(prefix, '', cleaned, flags=re.IGNORECASE)

            cleaned = cleaned.strip().strip('"\'').strip()

            # Take first line if multi-line
            if '\n' in cleaned:
                cleaned = cleaned.split('\n')[0].strip()

            # Remove trailing periods and extra punctuation
            cleaned = re.sub(r'[.!?]+$', '', cleaned)

            if 3 <= len(cleaned) <= 100 and not cleaned.lower() in ["unknown", "unclear"]:
                return cleaned, 0.8
            return None, 0.1

        else:
            # Generic processing for other fields
            import re
            cleaned = raw_response.strip().strip('"\'')

            # Remove verbose prefixes for all fields
            prefixes_to_remove = [
                r'^.*?based on.*?content.*?is:?\s*',
                r'^.*?after reviewing.*?is:?\s*',
                r'^.*?from.*?content.*?is:?\s*',
                r'^.*?located.*?is:?\s*',
                r'^.*?plant.*?is:?\s*',
                r'^.*?type.*?is:?\s*',
                r'^.*?address.*?is:?\s*',
                r'^.*?name.*?is:?\s*',
                r'^.*?is:?\s*',
                r'^the\s+',
                r'^answer:?\s*',
                r'^result:?\s*'
            ]

            for prefix in prefixes_to_remove:
                cleaned = re.sub(prefix, '', cleaned, flags=re.IGNORECASE)

            # Take first line if multi-line (unless it's JSON)
            if '\n' in cleaned and not (cleaned.strip().startswith('[') or cleaned.strip().startswith('{')):
                cleaned = cleaned.split('\n')[0].strip()

            # Remove trailing periods and extra punctuation
            cleaned = re.sub(r'[.!?]+$', '', cleaned)
            cleaned = cleaned.strip()

            if len(cleaned) > 0 and len(cleaned) < 500:  # Increased limit for JSON responses
                return cleaned, 0.7
            return None, 0.1

    def _parse_nested_json_with_pydantic(self, field_name: str, raw_response: str) -> tuple:
        """Parse nested JSON using Pydantic models for validation."""
        import json
        import re
        from typing import List, Dict, Any

        try:
            # Import Pydantic models
            from src.models import GridConnectivityMap, PPADetails

            # Clean the response first
            cleaned_response = raw_response.strip()

            # Try to extract JSON from the response
            json_data = self._extract_json_from_response(cleaned_response)

            if not json_data:
                logger.warning(f"No valid JSON found in response for {field_name}")
                return [], 0.1

            # Parse using appropriate Pydantic model
            if field_name == "grid_connectivity_maps":
                return self._parse_grid_connectivity_with_pydantic(json_data)
            elif field_name == "ppa_details":
                return self._parse_ppa_details_with_pydantic(json_data)
            else:
                return [], 0.1

        except Exception as e:
            logger.error(f"Error parsing nested JSON for {field_name}: {e}")
            return [], 0.1

    def _extract_json_from_response(self, response: str) -> Any:
        """Extract JSON from LLM response."""
        import json

        # Look for JSON patterns
        if '[' in response or '{' in response:
            # Find JSON part in the response
            start_idx = max(response.find('['), response.find('{'))
            if start_idx != -1:
                json_part = response[start_idx:]

                # Find the end of JSON
                if json_part.startswith('['):
                    end_idx = json_part.rfind(']') + 1
                else:
                    end_idx = json_part.rfind('}') + 1

                if end_idx > 0:
                    json_str = json_part[:end_idx]
                    try:
                        return json.loads(json_str)
                    except json.JSONDecodeError as e:
                        logger.warning(f"JSON decode error: {e}")
                        # Try to fix common JSON issues
                        return self._fix_and_parse_json(json_str)

        return None

    def _fix_and_parse_json(self, json_str: str) -> Any:
        """Attempt to fix common JSON issues and parse."""
        import json
        import re

        try:
            # Fix common issues
            fixed_json = json_str

            # Fix trailing commas
            fixed_json = re.sub(r',(\s*[}\]])', r'\1', fixed_json)

            # Fix single quotes to double quotes
            fixed_json = re.sub(r"'([^']*)':", r'"\1":', fixed_json)
            fixed_json = re.sub(r":\s*'([^']*)'", r': "\1"', fixed_json)

            # Fix unquoted keys
            fixed_json = re.sub(r'(\w+):', r'"\1":', fixed_json)

            return json.loads(fixed_json)

        except json.JSONDecodeError:
            logger.warning("Could not fix JSON format")
            return None

    def _parse_grid_connectivity_with_pydantic(self, json_data: Any) -> tuple:
        """Parse grid connectivity using Pydantic model."""
        from src.models import GridConnectivityMap, SubstationDetails

        try:
            validated_maps = []

            # Handle different input formats
            if isinstance(json_data, list):
                for item in json_data:
                    if isinstance(item, dict):
                        # Create GridConnectivityMap with validation
                        grid_map = GridConnectivityMap(
                            description=item.get("description", "Grid connectivity information"),
                            details=self._parse_substation_details(item.get("details", []))
                        )
                        validated_maps.append(grid_map.dict())

            elif isinstance(json_data, dict):
                # Single grid connectivity item
                grid_map = GridConnectivityMap(
                    description=json_data.get("description", "Grid connectivity information"),
                    details=self._parse_substation_details(json_data.get("details", []))
                )
                validated_maps.append(grid_map.dict())

            if validated_maps:
                logger.info(f"Successfully parsed {len(validated_maps)} grid connectivity maps with Pydantic")
                return validated_maps, 0.9
            else:
                return [], 0.2

        except Exception as e:
            logger.error(f"Pydantic validation error for grid connectivity: {e}")
            return [], 0.1

    def _parse_substation_details(self, details_data: Any) -> List[Dict]:
        """Parse substation details with Pydantic validation."""
        from src.models import SubstationDetails, SubstationProject

        validated_details = []

        if isinstance(details_data, list):
            for detail in details_data:
                if isinstance(detail, dict):
                    try:
                        # Parse projects if present
                        projects = []
                        if "projects" in detail and isinstance(detail["projects"], list):
                            for proj in detail["projects"]:
                                if isinstance(proj, dict):
                                    project = SubstationProject(
                                        description=proj.get("description", ""),
                                        distance=proj.get("distance", "")
                                    )
                                    projects.append(project.dict())

                        # Create SubstationDetails with validation
                        substation = SubstationDetails(
                            description=detail.get("description", ""),
                            capacity=detail.get("capacity", ""),
                            latitude=detail.get("latitude", ""),
                            longitude=detail.get("longitude", ""),
                            projects=projects,
                            substation_name=detail.get("substation_name", ""),
                            substation_type=detail.get("substation_type", "")
                        )
                        validated_details.append(substation.dict())

                    except Exception as e:
                        logger.warning(f"Error validating substation detail: {e}")
                        continue

        return validated_details

    def _parse_ppa_details_with_pydantic(self, json_data: Any) -> tuple:
        """Parse PPA details using Pydantic model."""
        from src.models import PPADetails, PPARespondent

        try:
            validated_ppas = []

            # Handle different input formats
            if isinstance(json_data, list):
                for item in json_data:
                    if isinstance(item, dict):
                        # Parse respondents
                        respondents = self._parse_ppa_respondents(item.get("respondents", []))

                        # Create PPADetails with validation
                        ppa = PPADetails(
                            description=item.get("description", "Power Purchase Agreement"),
                            capacity=item.get("capacity", ""),
                            capacity_unit=item.get("capacity_unit", "MW"),
                            start_date=item.get("start_date", ""),
                            end_date=item.get("end_date", ""),
                            tenure=item.get("tenure"),
                            tenure_type=item.get("tenure_type", "Years"),
                            respondents=respondents
                        )
                        validated_ppas.append(ppa.dict())

            elif isinstance(json_data, dict):
                # Single PPA item
                respondents = self._parse_ppa_respondents(json_data.get("respondents", []))

                ppa = PPADetails(
                    description=json_data.get("description", "Power Purchase Agreement"),
                    capacity=json_data.get("capacity", ""),
                    capacity_unit=json_data.get("capacity_unit", "MW"),
                    start_date=json_data.get("start_date", ""),
                    end_date=json_data.get("end_date", ""),
                    tenure=json_data.get("tenure"),
                    tenure_type=json_data.get("tenure_type", "Years"),
                    respondents=respondents
                )
                validated_ppas.append(ppa.dict())

            if validated_ppas:
                logger.info(f"Successfully parsed {len(validated_ppas)} PPA details with Pydantic")
                return validated_ppas, 0.9
            else:
                return [], 0.2

        except Exception as e:
            logger.error(f"Pydantic validation error for PPA details: {e}")
            return [], 0.1

    def _parse_ppa_respondents(self, respondents_data: Any) -> List[Dict]:
        """Parse PPA respondents with Pydantic validation."""
        from src.models import PPARespondent

        validated_respondents = []

        if isinstance(respondents_data, list):
            for resp in respondents_data:
                if isinstance(resp, dict):
                    try:
                        respondent = PPARespondent(
                            name=resp.get("name", ""),
                            capacity=resp.get("capacity", ""),
                            currency=resp.get("currency", ""),
                            price=resp.get("price", ""),
                            price_unit=resp.get("price_unit", "")
                        )
                        validated_respondents.append(respondent.dict())

                    except Exception as e:
                        logger.warning(f"Error validating PPA respondent: {e}")
                        continue
                elif isinstance(resp, str):
                    # Handle string respondents
                    respondent = PPARespondent(
                        name=resp,
                        capacity="",
                        currency="",
                        price="",
                        price_unit=""
                    )
                    validated_respondents.append(respondent.dict())

        return validated_respondents

    def test_connection(self) -> bool:
        """Test the Bedrock connection."""
        try:
            test_prompt = "What is 2+2? Answer with just the number."
            response = self.llm.invoke(test_prompt)

            if response and "4" in response:
                logger.info("Bedrock connection test successful")
                return True
            else:
                logger.warning(f"Bedrock connection test failed: {response}")
                return False

        except Exception as e:
            logger.error(f"Bedrock connection test failed: {e}")
            return False
